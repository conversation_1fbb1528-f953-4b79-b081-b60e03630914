from curl_cffi import requests
import json
import base64
import time
import uuid
from datetime import datetime

def check_token_validity(auth_cookie):
    """Check if the authentication token is valid and not expired"""
    try:
        # Extract the base64 part after 'base64-'
        if auth_cookie.startswith('base64-'):
            token_data = auth_cookie[7:]  # Remove 'base64-' prefix

            # Decode the base64 token
            decoded_bytes = base64.b64decode(token_data + '==')  # Add padding if needed
            token_info = json.loads(decoded_bytes.decode('utf-8'))

            # Check expiration
            expires_at = token_info.get('expires_at', 0)
            current_time = int(time.time())

            print(f"Token expires at: {datetime.fromtimestamp(expires_at)}")
            print(f"Current time: {datetime.fromtimestamp(current_time)}")

            if current_time >= expires_at:
                print("❌ Token has expired!")
                return False, token_info
            else:
                print("✅ Token is still valid")
                return True, token_info

    except Exception as e:
        print(f"❌ Error checking token: {e}")
        return False, None

def refresh_token_automatically(token_info, cookies):
    """Automatically refresh the token using the refresh token"""
    print("🔄 Attempting automatic token refresh...")

    if not token_info or 'refresh_token' not in token_info:
        print("❌ No refresh token available")
        return None

    refresh_token = token_info['refresh_token']
    print(f"🔑 Using refresh token: {refresh_token[:20]}...")

    # Supabase refresh endpoint
    refresh_url = 'https://huozgeqzcrkvtwrvoqsl.supabase.co/auth/v1/token?grant_type=refresh_token'

    headers = {
        'accept': 'application/json',
        'content-type': 'application/json',
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh1b3pnZXF6Y3JrdnR3cnZvcXNsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5NDIyNzYsImV4cCI6MjA2MzUxODI3Nn0.95xRjlxklVMgfaeaiSX8gGe_IdHKq7i-BLfekJ96COU',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    }

    payload = {
        'refresh_token': refresh_token
    }

    try:
        response = requests.post(
            refresh_url,
            headers=headers,
            json=payload,
            impersonate="chrome110"
        )

        print(f"Refresh Status: {response.status_code}")

        if response.status_code == 200:
            new_token_data = response.json()
            print("✅ Token refresh successful!")

            # Create new auth cookie
            new_token_json = json.dumps(new_token_data)
            new_encoded_token = base64.b64encode(new_token_json.encode('utf-8')).decode('utf-8')
            new_auth_cookie = f"base64-{new_encoded_token}"

            print(f"🎉 New token expires at: {datetime.fromtimestamp(new_token_data.get('expires_at', 0))}")

            return new_auth_cookie
        else:
            print(f"❌ Refresh failed: {response.text}")
            return None

    except Exception as e:
        print(f"❌ Error refreshing token: {e}")
        return None

def create_anonymous_session():
    """Create a new anonymous session if refresh fails"""
    print("🆕 Creating new anonymous session...")

    signup_url = 'https://huozgeqzcrkvtwrvoqsl.supabase.co/auth/v1/signup'

    headers = {
        'accept': 'application/json',
        'content-type': 'application/json',
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh1b3pnZXF6Y3JrdnR3cnZvcXNsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5NDIyNzYsImV4cCI6MjA2MzUxODI3Nn0.95xRjlxklVMgfaeaiSX8gGe_IdHKq7i-BLfekJ96COU',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    }

    # Anonymous signup payload
    payload = {
        'email': '',
        'password': '',
        'data': {},
        'gotrue_meta_security': {}
    }

    try:
        response = requests.post(
            signup_url,
            headers=headers,
            json=payload,
            impersonate="chrome110"
        )

        print(f"Anonymous Session Status: {response.status_code}")

        if response.status_code in [200, 201]:
            session_data = response.json()
            print("✅ Anonymous session created!")

            # Create new auth cookie
            new_token_json = json.dumps(session_data)
            new_encoded_token = base64.b64encode(new_token_json.encode('utf-8')).decode('utf-8')
            new_auth_cookie = f"base64-{new_encoded_token}"

            print(f"🎉 New session expires at: {datetime.fromtimestamp(session_data.get('expires_at', 0))}")

            return new_auth_cookie
        else:
            print(f"❌ Anonymous session creation failed: {response.text}")
            return None

    except Exception as e:
        print(f"❌ Error creating anonymous session: {e}")
        return None

def create_new_evaluation_session(cookies):
    """Create a new evaluation session for the new token"""
    print("🎯 Creating new evaluation session...")

    # Generate a new session ID
    new_session_id = str(uuid.uuid4())
    print(f"📝 Generated new session ID: {new_session_id}")

    # Try to visit the main page first to establish session
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'accept-language': 'en-US,en;q=0.9',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Linux"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
    }

    try:
        # Visit the main page to establish session
        print("🌐 Visiting main page to establish session...")
        response = requests.get(
            'https://alpha.lmarena.ai/',
            cookies=cookies,
            headers=headers,
            impersonate="chrome110"
        )

        print(f"Main page status: {response.status_code}")

        if response.status_code == 200:
            print("✅ Successfully accessed main page")

            # Try to visit the new session URL to create it
            session_url = f'https://alpha.lmarena.ai/c/{new_session_id}'
            print(f"🔗 Creating session at: {session_url}")

            session_response = requests.get(
                session_url,
                cookies=cookies,
                headers=headers,
                impersonate="chrome110"
            )

            print(f"Session creation status: {session_response.status_code}")

            if session_response.status_code == 200:
                print("✅ New evaluation session created successfully!")
                return new_session_id
            else:
                print(f"❌ Failed to create session: {session_response.status_code}")
                return None
        else:
            print(f"❌ Failed to access main page: {response.status_code}")
            return None

    except Exception as e:
        print(f"❌ Error creating evaluation session: {e}")
        return None



# Fresh cookies from current browser session with valid auth token
cookies = {
    '_ga': 'GA1.1.617101004.1746269484',
    '_gcl_au': '1.1.933930721.1746269484',
    '_gcl_gs': '2.1.k1$i1748098648$u220784645',
    '_gcl_aw': 'GCL.1748098655.CjwKCAjw3MXBBhAzEiwA0vLXQeojmGzkP9DdTTJcABfDz-jqMMC4XNPV0v71-DY3vd9X9h-GSXleXxoCGwgQAvD_BwE',
    '_ga_82JVGLVRQH': 'GS2.1.s1748098652$o3$g1$t1748098655$j0$l0$h0',
    '_ga_72FK1TMV06': 'GS2.1.s1748098666$o3$g1$t1748098677$j0$l0$h0',
    'arena-auth-prod-v1': 'base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0ltdHBaQ0k2SWtOVFQwNHhkM05uU0hkRlNFTkNNbGNpTENKMGVYQWlPaUpLVjFRaWZRLmV5SnBjM01pT2lKb2RIUndjem92TDJoMWIyZDZiMlZ4ZW1OeVpIWnJkM1IyYjJScExuTjFjR0ZpWVhObExtTnZMMkYxZEdndmRqRWlMQ0p6ZFdJaU9pSmtaalJsTjJVMk5DMDVNak0zTFRRNU5tWXRZbUZsWVMxbE5ESXlaamd3WVdZNE1HWWlMQ0poZFdRaU9pSmhkWFJvWlc1MGFXTmhkR1ZrSWl3aVpYaHdJam94TnpRNE1UQTBNRFF4TENKcFlYUWlPakUzTkRneE1EQTBOREVzSW1WdFlXbHNJam9pSWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT250OUxDSjFjMlZ5WDIxbGRHRmtZWFJoSWpwN0ltbGtJam9pWmpBeFpqRTBORFF0T1Rjek1pMDBaRFk1TFdKa1ltUXRNekEwWTJNeVpUUXpabVF3SW4wc0luSnZiR1VpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWVdGc0lqb2lZV0ZzTVNJc0ltRnRjaUk2VzNzaWJXVjBhRzlrSWpvaVlXNXZibmx0YjNWeklpd2lkR2x0WlhOMFlXMXdJam94TnpRNE1UQXdORFF4ZlYwc0luTmxjM05wYjI1ZmFXUWlPaUkxWVRGa09XSmtZaTB6WkRNMUxUUmtZMlV0WWpnd05pMDVNekZqTkdNeFkyRTBNV01pTENKcGMxOWhibTl1ZVcxdmRYTWlPblJ5ZFdWOS54RFlnVVEzMjF3bmpVOFZpcjdhMEY3anRmZlNEVmk4UUxscVdneWlwSGVRIiwidG9rZW5fdHlwZSI6ImJlYXJlciIsImV4cGlyZXNfaW4iOjM2MDAsImV4cGlyZXNfYXQiOjE3NDgxMDQwNDEsInJlZnJlc2hfdG9rZW4iOiJrcGhiY2JxajdzMmMiLCJ1c2VyIjp7ImlkIjoiZGY0ZTdlNjQtOTIzNy00OTZmLWJhZWEtZTQyMmY4MGFmODBmIiwiYXVkIjoiYXV0aGVudGljYXRlZCIsInJvbGUiOiJhdXRoZW50aWNhdGVkIiwiZW1haWwiOiIiLCJwaG9uZSI6IiIsImxhc3Rfc2lnbl9pbl9hdCI6IjIwMjUtMDUtMjRUMTU6Mjc6MjEuMzUxODM0Mzc5WiIsImFwcF9tZXRhZGF0YSI6e30sInVzZXJfbWV0YWRhdGEiOnsiaWQiOiJmMDFmMTQ0NC05NzMyLTRkNjktYmRiZC0zMDRjYzJlNDNmZDAifSwiaWRlbnRpdGllcyI6W10sImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTI0VDE1OjI3OjIxLjM0OTQ5OVoiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNS0yNFQxNToyNzoyMS4zNTMyODdaIiwiaXNfYW5vbnltb3VzIjp0cnVlfX0',
    '_ga_L5C4D55WJJ': 'GS2.1.s1748100440$o1$g1$t1748101425$j0$l0$h0',
    'sidebar': 'false',
    'ph_phc_LG7IJbVJqBsk584rbcKca0D5lV2vHguiijDrVji7yDM_posthog': '%7B%22distinct_id%22%3A%22f01f1444-9732-4d69-bdbd-304cc2e43fd0%22%2C%22%24sesid%22%3A%5B1748101725364%2C%22019702cc-cd88-7feb-9c38-2ccf5d5208a8%22%2C1748098665864%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Fbeta.lmarena.ai%2F%22%7D%7D',
}

headers = {
    'accept': '*/*',
    'accept-language': 'en-US,en;q=0.9',
    'cache-control': 'no-cache',
    'content-type': 'application/json;charset=UTF-8',
    'origin': 'https://beta.lmarena.ai',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://beta.lmarena.ai/c/8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Linux"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    # 'cookie': '_ga=GA1.1.*********.1747941054; _ga_L5C4D55WJJ=GS2.1.s1747941053$o1$g1$t1747942187$j0$l0$h0; cf_clearance=uTX4g1C91EAtAXe_oD1.krM4FqVuFauH_RL4C3TzrQ8-1747942273-*******-Dwq_rGO.Yd.csdmEMtD842jeDMZ2Fki3QbOWASM64wbCLCHuOrHDxyC1airydBrKGxZW3sltwrAr4HFzzyKPS_hQ6X8D.mMFA5Ch1mWcIvHulkmLaDZDRuYON43x2ATkF8WRJkEDQ4rjdAMStCLfeRUdp1LnvbRzMzepdUlsSbfY4tX37Z_B9Fir_Jm7aJdSMVqoT5jtlOS_6FLfwaHdSM0Rj9vyofwoYrtBZ9m0PxeWU2e.I8rgzcbWiWB5kHy9_iBkzEh8pAZ.uIf2ZUEcG59YVAYU1QmM5m8NvvTZVdPglO.vJol9NPZHyWQiQ5kEG6svlFc65j7UzvjicJX9KIpSc9O3x3B4ENhmVHyqKgO98Eh9uB.o8.BswUglX0tg; arena-auth-prod-v1=base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0ltdHBaQ0k2SWtOVFQwNHhkM05uU0hkRlNFTkNNbGNpTENKMGVYQWlPaUpLVjFRaWZRLmV5SnBjM01pT2lKb2RIUndjem92TDJoMWIyZDZiMlZ4ZW1OeVpIWnJkM1IyYjJScExuTjFjR0ZpWVhObExtTnZMMkYxZEdndmRqRWlMQ0p6ZFdJaU9pSTFOek0wWTJWa1pTMHlNVFkzTFRRM09UUXRZVEkyWXkxbVpURmpaRGhrTWpjNU5EUWlMQ0poZFdRaU9pSmhkWFJvWlc1MGFXTmhkR1ZrSWl3aVpYaHdJam94TnpRM09UUTFPRGMyTENKcFlYUWlPakUzTkRjNU5ESXlOellzSW1WdFlXbHNJam9pSWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT250OUxDSjFjMlZ5WDIxbGRHRmtZWFJoSWpwN0ltbGtJam9pTUdSbFlqYzJPRGd0WVRGa09DMDBPVFE0TFRreU16UXRNbUk1WW1abVpUQTRZbVE0SW4wc0luSnZiR1VpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWVdGc0lqb2lZV0ZzTVNJc0ltRnRjaUk2VzNzaWJXVjBhRzlrSWpvaVlXNXZibmx0YjNWeklpd2lkR2x0WlhOMFlXMXdJam94TnpRM09UUXlNamMyZlYwc0luTmxjM05wYjI1ZmFXUWlPaUkyTVdReE5qWTBNaTFsTUdJeExUUTNNMk10WVRjMk9TMW1PRFJtTkRGbU1EZ3hNV0lpTENKcGMxOWhibTl1ZVcxdmRYTWlPblJ5ZFdWOS45NXhSamx4a2xWTWdmYWVhaVNYOGdHZV9JZEhLcTdpLUJMZmVrSjk2Q09VIiwidG9rZW5fdHlwZSI6ImJlYXJlciIsImV4cGlyZXNfaW4iOjM2MDAsImV4cGlyZXNfYXQiOjE3NDc5NDU4NzYsInJlZnJlc2hfdG9rZW4iOiJzZXRvZm9wZ3hhdmwiLCJ1c2VyIjp7ImlkIjoiNTczNGNlZGUtMjE2Ny00Nzk0LWEyNmMtZmUxY2Q4ZDI3OTQ0IiwiYXVkIjoiYXV0aGVudGljYXRlZCIsInJvbGUiOiJhdXRoZW50aWNhdGVkIiwiZW1haWwiOiIiLCJwaG9uZSI6IiIsImxhc3Rfc2lnbl9pbl9hdCI6IjIwMjUtMDUtMjJUMTk6MzE6MTYuNzM3MDg5Nzc0WiIsImFwcF9tZXRhZGF0YSI6e30sInVzZXJfbWV0YWRhdGEiOnsiaWQiOiIwZGViNzY4OC1hMWQ4LTQ5NDgtOTIzNC0yYjliZmZlMDhiZDgifSwiaWRlbnRpdGllcyI6W10sImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTIyVDE5OjMxOjE2LjczNTQ3OFoiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNS0yMlQxOTozMToxNi43Mzg1MDlaIiwiaXNfYW5vbnltb3VzIjp0cnVlfX0; ph_phc_LG7IJbVJqBsk584rbcKca0D5lV2vHguiijDrVji7yDM_posthog=%7B%22distinct_id%22%3A%220deb7688-a1d8-4948-9234-2b9bffe08bd8%22%2C%22%24sesid%22%3A%5B1747942388004%2C%220196f967-d47f-7c28-af63-5b0f114edc56%22%2C1747941053567%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Falpha.lmarena.ai%2F%22%7D%7D; _ga_72FK1TMV06=GS2.1.s1747942207$o1$g1$t1747942388$j0$l0$h0',
}

# Generate fresh UUIDs to avoid conflicts
user_msg_id_1 = str(uuid.uuid4())
assistant_msg_id_1 = str(uuid.uuid4())
user_msg_id_2 = str(uuid.uuid4())
assistant_msg_id_2 = str(uuid.uuid4())

print(f"🆔 Generated fresh message IDs:")
print(f"   User message 1: {user_msg_id_1}")
print(f"   Assistant message 1: {assistant_msg_id_1}")
print(f"   User message 2: {user_msg_id_2}")
print(f"   Assistant message 2: {assistant_msg_id_2}")

# Updated data payload for battle mode with fresh message IDs
data_dict = {
    "id": "7c05b741-0e4a-4d8d-bd5c-5ae0e6b801d6",  # Updated to your session
    "mode": "battle",  # Changed to battle mode
    "userMessageId": user_msg_id_2,  # Use the second user message
    "modelAMessageId": assistant_msg_id_2,  # Use the second assistant message
    "modelBMessageId": str(uuid.uuid4()),  # Fresh model B message ID
    "messages": [
        {
            "id": user_msg_id_1,  # Fresh UUID
            "role": "user",
            "content": "hi",
            "experimental_attachments": [],
            "parentMessageIds": [],
            "participantPosition": "a",
            "modelId": None,
            "evaluationSessionId": "7c05b741-0e4a-4d8d-bd5c-5ae0e6b801d6",
            "status": "pending",
            "failureReason": None
        },
        {
            "id": assistant_msg_id_1,  # Fresh UUID
            "role": "assistant",
            "content": "Hello! How can I help you today?",
            "experimental_attachments": [],
            "parentMessageIds": [user_msg_id_1],  # Reference fresh user message ID
            "participantPosition": "a",
            "modelId": None,  # No specific model ID in battle mode
            "evaluationSessionId": "7c05b741-0e4a-4d8d-bd5c-5ae0e6b801d6",
            "status": "success",
            "failureReason": None
        },
        {
            "id": user_msg_id_2,  # Fresh UUID
            "role": "user",
            "content": "what model are you",
            "experimental_attachments": [],
            "parentMessageIds": [assistant_msg_id_1],  # Reference fresh assistant message ID
            "participantPosition": "a",
            "modelId": None,
            "evaluationSessionId": "7c05b741-0e4a-4d8d-bd5c-5ae0e6b801d6",
            "status": "pending",
            "failureReason": None
        },
        {
            "id": assistant_msg_id_2,  # Fresh UUID
            "role": "assistant",
            "content": "",
            "experimental_attachments": [],
            "parentMessageIds": [user_msg_id_2],  # Reference fresh user message ID
            "participantPosition": "a",
            "modelId": None,  # No specific model ID in battle mode
            "evaluationSessionId": "7c05b741-0e4a-4d8d-bd5c-5ae0e6b801d6",
            "status": "pending",
            "failureReason": None
        }
    ],
    "modality": "chat"
}



# Check token validity and automatically refresh if needed
print("🔍 Checking authentication token validity...")
auth_token = cookies.get('arena-auth-prod-v1', '')
is_valid, token_info = check_token_validity(auth_token)

# Default session ID (current working session)
session_id = "7c05b741-0e4a-4d8d-bd5c-5ae0e6b801d6"
token_refreshed = False

if not is_valid:
    print("🔄 Token expired! Attempting automatic refresh...")

    # Try to refresh the token
    new_auth_token = refresh_token_automatically(token_info, cookies)

    if new_auth_token:
        print("✅ Token refreshed successfully!")
        cookies['arena-auth-prod-v1'] = new_auth_token
        token_refreshed = True

        # Create new evaluation session for the refreshed token
        new_session_id = create_new_evaluation_session(cookies)
        if new_session_id:
            session_id = new_session_id
            print(f"🎯 Using new evaluation session: {session_id}")
        else:
            print("⚠️  Could not create new evaluation session, using original session")

    else:
        print("🔄 Refresh failed, trying to create new anonymous session...")
        new_auth_token = create_anonymous_session()

        if new_auth_token:
            print("✅ New anonymous session created!")
            cookies['arena-auth-prod-v1'] = new_auth_token
            token_refreshed = True

            # Create new evaluation session for the new anonymous token
            new_session_id = create_new_evaluation_session(cookies)
            if new_session_id:
                session_id = new_session_id
                print(f"🎯 Using new evaluation session: {session_id}")
            else:
                print("⚠️  Could not create new evaluation session, using original session")

        else:
            print("❌ All automatic token generation attempts failed!")
            print("💡 You may need to manually get a fresh authentication token from your browser.")
            print("💡 The script will continue with the current token, but may fail.")

# Set up the API URL for the session (either original or newly created)
api_url = f'https://beta.lmarena.ai/api/stream/post-to-evaluation/{session_id}'
headers['referer'] = f'https://beta.lmarena.ai/c/{session_id}'

# Update the data with the correct session ID
data_dict["id"] = session_id
for message in data_dict["messages"]:
    message["evaluationSessionId"] = session_id

if token_refreshed:
    print(f"🔄 Updated all message session IDs to: {session_id}")

# Convert to JSON string
data = json.dumps(data_dict)

print("\n🚀 Making API request...")

try:
    # Try with the beta URL
    response = requests.post(
        api_url,
        cookies=cookies,
        headers=headers,
        data=data,
        impersonate="chrome110"
    )
    print("📊 Beta Domain Response:")
    print("Status Code:", response.status_code)
    print("Response Text:", response.text)

    # Check if response contains error
    if response.status_code != 200 or "error" in response.text:
        print("\n❌ Error detected in beta response, trying with alpha domain...")

        # Try with alpha domain instead of beta
        alpha_headers = headers.copy()
        alpha_headers['origin'] = 'https://alpha.lmarena.ai'
        alpha_headers['referer'] = f'https://alpha.lmarena.ai/c/{session_id}'
        alpha_url = f'https://alpha.lmarena.ai/api/stream/post-to-evaluation/{session_id}'

        response_alpha = requests.post(
            alpha_url,
            cookies=cookies,
            headers=alpha_headers,
            data=data,
            impersonate="chrome110"
        )
        print("\n📊 Alpha Domain Response:")
        print("Status Code:", response_alpha.status_code)
        print("Response Text:", response_alpha.text)

        if response_alpha.status_code == 200 and "error" not in response_alpha.text:
            print("✅ Alpha domain request succeeded!")
        else:
            print("❌ Both beta and alpha domains failed.")
            print("💡 Possible issues:")
            print("   - Authentication token expired")
            print("   - Session ID no longer valid")
            print("   - Message IDs already processed")
            print("   - Server-side database issues")
            print("   - Need to create evaluation through web interface first")
    else:
        print("✅ Beta domain request succeeded!")

except Exception as e:
    print(f"💥 Exception occurred: {e}")
    print("💡 This could be due to:")
    print("   - Network connectivity issues")
    print("   - Invalid request format")
    print("   - Missing dependencies")

