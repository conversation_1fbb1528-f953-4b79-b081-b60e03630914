from curl_cffi import requests
import json
import base64
import time
from datetime import datetime

def check_token_validity(auth_cookie):
    """Check if the authentication token is valid and not expired"""
    try:
        # Extract the base64 part after 'base64-'
        if auth_cookie.startswith('base64-'):
            token_data = auth_cookie[7:]  # Remove 'base64-' prefix

            # Decode the base64 token
            decoded_bytes = base64.b64decode(token_data + '==')  # Add padding if needed
            token_info = json.loads(decoded_bytes.decode('utf-8'))

            # Check expiration
            expires_at = token_info.get('expires_at', 0)
            current_time = int(time.time())

            print(f"Token expires at: {datetime.fromtimestamp(expires_at)}")
            print(f"Current time: {datetime.fromtimestamp(current_time)}")

            if current_time >= expires_at:
                print("❌ Token has expired!")
                return False
            else:
                print("✅ Token is still valid")
                return True

    except Exception as e:
        print(f"❌ Error checking token: {e}")
        return False

# Updated cookies with fresh authentication token
cookies = {
    '_ga': 'GA1.1.617101004.1746269484',
    '_gcl_au': '1.1.933930721.1746269484',
    '_gcl_gs': '2.1.k1$i1748098648$u220784645',
    '_gcl_aw': 'GCL.1748098655.CjwKCAjw3MXBBhAzEiwA0vLXQeojmGzkP9DdTTJcABfDz-jqMMC4XNPV0v71-DY3vd9X9h-GSXleXxoCGwgQAvD_BwE',
    '_ga_82JVGLVRQH': 'GS2.1.s1748098652$o3$g1$t1748098655$j0$l0$h0',
    'arena-auth-prod-v1': 'base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0ltdHBaQ0k2SWtOVFQwNHhkM05uU0hkRlNFTkNNbGNpTENKMGVYQWlPaUpLVjFRaWZRLmV5SnBjM01pT2lKb2RIUndjem92TDJoMWIyZDZiMlZ4ZW1OeVpIWnJkM1IyYjJScExuTjFjR0ZpWVhObExtTnZMMkYxZEdndmRqRWlMQ0p6ZFdJaU9pSXdNVFE1WXpWa015MHlOVFptTFRRM04yUXRPREl3WlMxbE5UQmxNemt5WTJSbE1qTWlMQ0poZFdRaU9pSmhkWFJvWlc1MGFXTmhkR1ZrSWl3aVpYaHdJam94TnpRNE1UQXlNall4TENKcFlYUWlPakUzTkRnd09UZzJOakVzSW1WdFlXbHNJam9pSWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT250OUxDSjFjMlZ5WDIxbGRHRmtZWFJoSWpwN0ltbGtJam9pTUdGbE9XVXlNVEF0T1daaFl5MDBOakZoTFdFME0yTXROekZsWkdaaVpHUmxNV1F6SW4wc0luSnZiR1VpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWVdGc0lqb2lZV0ZzTVNJc0ltRnRjaUk2VzNzaWJXVjBhRzlrSWpvaVlXNXZibmx0YjNWeklpd2lkR2x0WlhOMFlXMXdJam94TnpRMk1qWTVOVEE0ZlYwc0luTmxjM05wYjI1ZmFXUWlPaUkwTWpBeE5UUmhZUzFpTnpKaUxUUm1ZVGN0T1dVMk5TMWhZek5oWmpkak5qSmlNREVpTENKcGMxOWhibTl1ZVcxdmRYTWlPblJ5ZFdWOS5ETVh6VWlJX2tsVnB6eWs2eFoxTWFHM1lYcVZMZnE3dEJ0LUpHSnhuRGtNIiwidG9rZW5fdHlwZSI6ImJlYXJlciIsImV4cGlyZXNfaW4iOjM2MDAsImV4cGlyZXNfYXQiOjE3NDgxMDIyNjEsInJlZnJlc2hfdG9rZW4iOiJobDZkc2tiand6ZG0iLCJ1c2VyIjp7ImlkIjoiMDE0OWM1ZDMtMjU2Zi00NzdkLTgyMGUtZTUwZTM5MmNkZTIzIiwiYXVkIjoiYXV0aGVudGljYXRlZCIsInJvbGUiOiJhdXRoZW50aWNhdGVkIiwiZW1haWwiOiIiLCJwaG9uZSI6IiIsImxhc3Rfc2lnbl9pbl9hdCI6IjIwMjUtMDUtMDNUMTA6NTE6NDguNzk0NDY5WiIsImFwcF9tZXRhZGF0YSI6e30sInVzZXJfbWV0YWRhdGEiOnsiaWQiOiIwYWU5ZTIxMC05ZmFjLTQ2MWEtYTQzYy03MWVkZmJkZGUxZDMifSwiaWRlbnRpdGllcyI6W10sImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTAzVDEwOjUxOjQ4Ljc5MjU0OFoiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNS0yNFQxNDo1Nzo0MS42MzQyNjlaIiwiaXNfYW5vbnltb3VzIjp0cnVlfX0',
    '_ga_72FK1TMV06': 'GS2.1.s1748098666$o3$g1$t1748098677$j0$l0$h0',
    'sidebar': 'false',
    'ph_phc_LG7IJbVJqBsk584rbcKca0D5lV2vHguiijDrVji7yDM_posthog': '%7B%22distinct_id%22%3A%220ae9e210-9fac-461a-a43c-71edfbdde1d3%22%2C%22%24sesid%22%3A%5B1748098724403%2C%22019702cc-cd88-7feb-9c38-2ccf5d5208a8%22%2C1748098665864%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Fbeta.lmarena.ai%2F%22%7D%7D',
}

headers = {
    'accept': '*/*',
    'accept-language': 'en-US,en;q=0.9',
    'cache-control': 'no-cache',
    'content-type': 'application/json;charset=UTF-8',
    'origin': 'https://beta.lmarena.ai',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://beta.lmarena.ai/c/8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Linux"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    # 'cookie': '_ga=GA1.1.*********.1747941054; _ga_L5C4D55WJJ=GS2.1.s1747941053$o1$g1$t1747942187$j0$l0$h0; cf_clearance=uTX4g1C91EAtAXe_oD1.krM4FqVuFauH_RL4C3TzrQ8-1747942273-*******-Dwq_rGO.Yd.csdmEMtD842jeDMZ2Fki3QbOWASM64wbCLCHuOrHDxyC1airydBrKGxZW3sltwrAr4HFzzyKPS_hQ6X8D.mMFA5Ch1mWcIvHulkmLaDZDRuYON43x2ATkF8WRJkEDQ4rjdAMStCLfeRUdp1LnvbRzMzepdUlsSbfY4tX37Z_B9Fir_Jm7aJdSMVqoT5jtlOS_6FLfwaHdSM0Rj9vyofwoYrtBZ9m0PxeWU2e.I8rgzcbWiWB5kHy9_iBkzEh8pAZ.uIf2ZUEcG59YVAYU1QmM5m8NvvTZVdPglO.vJol9NPZHyWQiQ5kEG6svlFc65j7UzvjicJX9KIpSc9O3x3B4ENhmVHyqKgO98Eh9uB.o8.BswUglX0tg; arena-auth-prod-v1=base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0ltdHBaQ0k2SWtOVFQwNHhkM05uU0hkRlNFTkNNbGNpTENKMGVYQWlPaUpLVjFRaWZRLmV5SnBjM01pT2lKb2RIUndjem92TDJoMWIyZDZiMlZ4ZW1OeVpIWnJkM1IyYjJScExuTjFjR0ZpWVhObExtTnZMMkYxZEdndmRqRWlMQ0p6ZFdJaU9pSTFOek0wWTJWa1pTMHlNVFkzTFRRM09UUXRZVEkyWXkxbVpURmpaRGhrTWpjNU5EUWlMQ0poZFdRaU9pSmhkWFJvWlc1MGFXTmhkR1ZrSWl3aVpYaHdJam94TnpRM09UUTFPRGMyTENKcFlYUWlPakUzTkRjNU5ESXlOellzSW1WdFlXbHNJam9pSWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT250OUxDSjFjMlZ5WDIxbGRHRmtZWFJoSWpwN0ltbGtJam9pTUdSbFlqYzJPRGd0WVRGa09DMDBPVFE0TFRreU16UXRNbUk1WW1abVpUQTRZbVE0SW4wc0luSnZiR1VpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWVdGc0lqb2lZV0ZzTVNJc0ltRnRjaUk2VzNzaWJXVjBhRzlrSWpvaVlXNXZibmx0YjNWeklpd2lkR2x0WlhOMFlXMXdJam94TnpRM09UUXlNamMyZlYwc0luTmxjM05wYjI1ZmFXUWlPaUkyTVdReE5qWTBNaTFsTUdJeExUUTNNMk10WVRjMk9TMW1PRFJtTkRGbU1EZ3hNV0lpTENKcGMxOWhibTl1ZVcxdmRYTWlPblJ5ZFdWOS45NXhSamx4a2xWTWdmYWVhaVNYOGdHZV9JZEhLcTdpLUJMZmVrSjk2Q09VIiwidG9rZW5fdHlwZSI6ImJlYXJlciIsImV4cGlyZXNfaW4iOjM2MDAsImV4cGlyZXNfYXQiOjE3NDc5NDU4NzYsInJlZnJlc2hfdG9rZW4iOiJzZXRvZm9wZ3hhdmwiLCJ1c2VyIjp7ImlkIjoiNTczNGNlZGUtMjE2Ny00Nzk0LWEyNmMtZmUxY2Q4ZDI3OTQ0IiwiYXVkIjoiYXV0aGVudGljYXRlZCIsInJvbGUiOiJhdXRoZW50aWNhdGVkIiwiZW1haWwiOiIiLCJwaG9uZSI6IiIsImxhc3Rfc2lnbl9pbl9hdCI6IjIwMjUtMDUtMjJUMTk6MzE6MTYuNzM3MDg5Nzc0WiIsImFwcF9tZXRhZGF0YSI6e30sInVzZXJfbWV0YWRhdGEiOnsiaWQiOiIwZGViNzY4OC1hMWQ4LTQ5NDgtOTIzNC0yYjliZmZlMDhiZDgifSwiaWRlbnRpdGllcyI6W10sImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTIyVDE5OjMxOjE2LjczNTQ3OFoiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNS0yMlQxOTozMToxNi43Mzg1MDlaIiwiaXNfYW5vbnltb3VzIjp0cnVlfX0; ph_phc_LG7IJbVJqBsk584rbcKca0D5lV2vHguiijDrVji7yDM_posthog=%7B%22distinct_id%22%3A%220deb7688-a1d8-4948-9234-2b9bffe08bd8%22%2C%22%24sesid%22%3A%5B1747942388004%2C%220196f967-d47f-7c28-af63-5b0f114edc56%22%2C1747941053567%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Falpha.lmarena.ai%2F%22%7D%7D; _ga_72FK1TMV06=GS2.1.s1747942207$o1$g1$t1747942388$j0$l0$h0',
}

# Parse the JSON data
data_dict = {
    "id": "8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4",
    "mode": "direct",
    "modelAId": "ee116d12-64d6-48a8-88e5-b2d06325cdd2",
    "userMessageId": "beab43e7-9a7e-42ad-9cec-67e545bf4e70",
    "modelAMessageId": "2fbf4011-4793-4da7-834b-b8d3bb02f7f0",
    "messages": [
        {
            "id": "f5ee49bb-c051-4126-9679-3c20a9d88292",
            "role": "user",
            "content": "hi",
            "experimental_attachments": [],
            "parentMessageIds": [],
            "participantPosition": "a",
            "modelId": None,
            "evaluationSessionId": "8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4",
            "status": "pending",
            "failureReason": None
        },
        {
            "id": "117276cc-11c9-4462-a080-d1c8c03ef51e",
            "role": "assistant",
            "content": "Hello! How can I help you today?",
            "experimental_attachments": [],
            "parentMessageIds": ["f5ee49bb-c051-4126-9679-3c20a9d88292"],
            "participantPosition": "a",
            "modelId": "ee116d12-64d6-48a8-88e5-b2d06325cdd2",
            "evaluationSessionId": "8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4",
            "status": "success",
            "failureReason": None
        },
        {
            "id": "beab43e7-9a7e-42ad-9cec-67e545bf4e70",
            "role": "user",
            "content": "test",
            "experimental_attachments": [],
            "parentMessageIds": ["117276cc-11c9-4462-a080-d1c8c03ef51e"],
            "participantPosition": "a",
            "modelId": None,
            "evaluationSessionId": "8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4",
            "status": "success",  # Changed from pending to success
            "failureReason": None
        },
        {
            "id": "2fbf4011-4793-4da7-834b-b8d3bb02f7f0",
            "role": "assistant",
            "content": "",
            "experimental_attachments": [],
            "parentMessageIds": ["beab43e7-9a7e-42ad-9cec-67e545bf4e70"],
            "participantPosition": "a",
            "modelId": "ee116d12-64d6-48a8-88e5-b2d06325cdd2",
            "evaluationSessionId": "8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4",
            "status": "pending",
            "failureReason": None
        }
    ],
    "modality": "chat"
}

def create_new_evaluation():
    """Create a new evaluation session"""
    print("🆕 Creating new evaluation session...")

    # Headers for creating evaluation
    eval_headers = headers.copy()
    eval_headers['referer'] = 'https://beta.lmarena.ai/'

    try:
        # Try to create a new evaluation session
        # This endpoint might vary - common patterns are /api/evaluation/new or /api/chat/new
        create_response = requests.post(
            'https://beta.lmarena.ai/api/chat/new',  # Common endpoint for new chat/evaluation
            cookies=cookies,
            headers=eval_headers,
            json={},  # Empty payload to start new session
            impersonate="chrome110"
        )

        print(f"Create Session Status: {create_response.status_code}")
        print(f"Create Session Response: {create_response.text}")

        if create_response.status_code == 200:
            response_data = create_response.json()
            session_id = response_data.get('id') or response_data.get('sessionId') or response_data.get('evaluationId')
            if session_id:
                print(f"✅ New evaluation session created: {session_id}")
                return session_id

        # If that doesn't work, try alternative endpoint
        print("Trying alternative endpoint...")
        create_response2 = requests.get(
            'https://beta.lmarena.ai/api/evaluation/new',
            cookies=cookies,
            headers=eval_headers,
            impersonate="chrome110"
        )

        print(f"Alt Create Status: {create_response2.status_code}")
        print(f"Alt Create Response: {create_response2.text}")

        return None

    except Exception as e:
        print(f"❌ Error creating evaluation: {e}")
        return None

# Check token validity before making requests
print("🔍 Checking authentication token validity...")
auth_token = cookies.get('arena-auth-prod-v1', '')
if not check_token_validity(auth_token):
    print("⚠️  Warning: Token appears to be expired or invalid. The request may fail.")
    print("💡 You may need to get a fresh authentication token from your browser.")

# Try to create a new evaluation session first
new_session_id = create_new_evaluation()

if new_session_id:
    print(f"\n🎯 Using new session ID: {new_session_id}")
    # Update the data with the new session ID
    data_dict["id"] = new_session_id
    for message in data_dict["messages"]:
        message["evaluationSessionId"] = new_session_id

    # Update the URL and referer
    api_url = f'https://beta.lmarena.ai/api/stream/post-to-evaluation/{new_session_id}'
    headers['referer'] = f'https://beta.lmarena.ai/c/{new_session_id}'
else:
    print("\n⚠️  Could not create new session, using original session ID")
    api_url = 'https://beta.lmarena.ai/api/stream/post-to-evaluation/8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4'

# Convert to JSON string (with potentially updated session ID)
data = json.dumps(data_dict)

print("\n🚀 Making API request...")

try:
    # Try with the beta URL
    response = requests.post(
        api_url,
        cookies=cookies,
        headers=headers,
        data=data,
        impersonate="chrome110"
    )
    print("📊 Beta Domain Response:")
    print("Status Code:", response.status_code)
    print("Response Text:", response.text)

    # Check if response contains error
    if response.status_code != 200 or "error" in response.text:
        print("\n❌ Error detected in beta response, trying with alpha domain...")

        # Try with alpha domain instead of beta
        alpha_headers = headers.copy()
        alpha_headers['origin'] = 'https://alpha.lmarena.ai'
        if new_session_id:
            alpha_headers['referer'] = f'https://alpha.lmarena.ai/c/{new_session_id}'
            alpha_url = f'https://alpha.lmarena.ai/api/stream/post-to-evaluation/{new_session_id}'
        else:
            alpha_headers['referer'] = 'https://alpha.lmarena.ai/c/8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4'
            alpha_url = 'https://alpha.lmarena.ai/api/stream/post-to-evaluation/8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4'

        response_alpha = requests.post(
            alpha_url,
            cookies=cookies,
            headers=alpha_headers,
            data=data,
            impersonate="chrome110"
        )
        print("\n📊 Alpha Domain Response:")
        print("Status Code:", response_alpha.status_code)
        print("Response Text:", response_alpha.text)

        if response_alpha.status_code == 200 and "error" not in response_alpha.text:
            print("✅ Alpha domain request succeeded!")
        else:
            print("❌ Both beta and alpha domains failed.")
            print("💡 Possible issues:")
            print("   - Authentication token expired")
            print("   - Session ID no longer valid")
            print("   - Message IDs already processed")
            print("   - Server-side database issues")
            print("   - Need to create evaluation through web interface first")
    else:
        print("✅ Beta domain request succeeded!")

except Exception as e:
    print(f"💥 Exception occurred: {e}")
    print("💡 This could be due to:")
    print("   - Network connectivity issues")
    print("   - Invalid request format")
    print("   - Missing dependencies")

print("\n💡 Alternative: Create evaluation manually:")
print("1. Go to https://beta.lmarena.ai/ or https://alpha.lmarena.ai/")
print("2. Start a new chat/evaluation")
print("3. Copy the session ID from the URL (e.g., /c/SESSION_ID)")
print("4. Update the script with the new session ID")